// For Loop function that counts from 1-10 with 0.5 second delay
async function runForLoop() {
    const output = document.querySelector('.for-loop-container .output');
    const button = document.querySelector('.for-loop-container button');

    output.innerHTML = '';
    button.textContent = 'Running...';
    button.disabled = true; // Disable button during loop execution

    for (let i = 1; i <= 10; i++) {
        output.innerHTML += `${i} `;

        // Wait for 0.5 seconds before the next iteration
        await new Promise(resolve => setTimeout(resolve, 500));
    }

    // Add completion message and change button to "Restart"
    button.textContent = 'Restart';
    button.disabled = false;
}

// While Loop function that adds random numbers until total > 30
async function runWhileLoop() {
    const output = document.querySelector('.while-loop-container .output');
    const button = document.querySelector('.while-loop-container button');

    output.innerHTML = '';
    button.textContent = 'Running...';
    button.disabled = true; // Disable button during loop execution

    let total = 0;
    let iteration = 1;

    // Add initial message
    output.innerHTML = '<div><strong>Adding random numbers until total > 30:</strong></div><br>';

    while (total <= 30) {
        // Generate random number between 1 and 10
        const randomNumber = Math.floor(Math.random() * 10) + 1;

        // Add to total
        total += randomNumber;

        // Display the current iteration with the random number and running total
        output.innerHTML += `<div>Step ${iteration}: Added ${randomNumber} → Running total: ${total}</div>`;

        // Increment iteration counter
        iteration++;

        // Wait for 1 second before the next iteration to see the progression
        await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // Change button to "Restart"
    button.textContent = 'Restart';
    button.disabled = false;
}

// forEach Loop function that iterates through an array
async function runForEachLoop() {
    const output = document.querySelector('.forEach-loop-container .output');
    const button = document.querySelector('.forEach-loop-container button');

    output.innerHTML = '';
    button.textContent = 'Running...';
    button.disabled = true; // Disable button during loop execution

    const fruits = ['Apple', 'Banana', 'Orange', 'Grape', 'Strawberry'];

    output.innerHTML = '<div><strong>Iterating through fruits array:</strong></div><br>';

    // Use forEach with async delay
    for (let i = 0; i < fruits.length; i++) {
        const fruit = fruits[i];
        output.innerHTML += `<div>Index ${i}: ${fruit}</div>`;

        // Wait for 0.8 seconds before the next iteration
        await new Promise(resolve => setTimeout(resolve, 800));
    }


    // Change button to "Restart"
    button.textContent = 'Restart';
    button.disabled = false;
}