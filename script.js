// For Loop function that counts from 1-10 with 0.5 second delay
async function runForLoop() {
    const output = document.querySelector('.for-loop-container .output');
    const button = document.querySelector('.for-loop-container button');

    output.innerHTML = '';
    button.textContent = 'Run';
    button.disabled = true; // Disable button during loop execution

    for (let i = 1; i <= 10; i++) {
        output.innerHTML += `${i} `;

        // Wait for 0.5 seconds before the next iteration
        await new Promise(resolve => setTimeout(resolve, 500));
    }

    // Add completion message and change button to "Restart"
    button.textContent = 'Restart';
    button.disabled = false; 
}



